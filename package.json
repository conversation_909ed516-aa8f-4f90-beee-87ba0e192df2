{"name": "miniapp", "version": "1.0.0", "description": "", "main": ".eslintrc.js", "dependencies": {"babel-eslint": "^10.1.0", "eslint": "^7.26.0"}, "devDependencies": {"eslint-plugin-html": "^6.1.2"}, "scripts": {"lint": "eslint --fix wxapp/", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "********************:frontend/patient-miniapp.git"}, "eslintIgnore": ["**/*wxml"], "author": "", "license": "ISC"}