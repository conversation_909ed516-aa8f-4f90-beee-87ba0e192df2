const api = require('../../config/api')
const util = require('../../utils/util')
Component({
  properties: {
    showAll: {
      type: Boolean,
      value: false
    }
  },
  data: {
    img_more_classification: api.ImgUrl + 'images/img_more_classification.png',
    img_default_classification: api.ImgUrl + 'images/<EMAIL>',
    list: [],
    allList: [],
    firstCategoryId: '',
  },
  lifetimes: {
    attached: function() {

    },
    detached: function() {

    }
  },
  pageLifetimes: {
    show: function() {
      this.getData()
    }
  },
  methods: {
    getData() {
      util.request(api.categoryList, {}, 'post').then(res => {
        let list = res.data.data.slice()
        if (list.length > 10 && !this.properties.showAll) {
          list = list.splice(0, 9)
        }
        this.setData({
          list,
          allList: res.data.data,
          firstCategoryId: list && list.length > 0 ? list[0].id : ''
        })
      })
    },
    goAllClassify() {
      if (!this.data.firstCategoryId) {
        return
      }
      wx.navigateTo({
        url: '/pages/allClassify/index?categoryId=' + this.data.firstCategoryId
      })
    },
    goSearchClassify(e) {
      const { id, name } = e.currentTarget.dataset.item
      wx.navigateTo({
        // url: `/pages/searchMerchandiseClassify/index?categoryId=${id}&categoryName=${name}`
        url: `/pages/allClassify/index?categoryId=${id}&categoryName=${name}`
      })
    }
  }
})
