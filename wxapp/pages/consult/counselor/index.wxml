<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="诊前咨询"
	pageNum="{{pageNum}}"></navbar>
<view class='chating-wrapper' catchtap='chatingWrapperClick'>
	<view id='dobBox' class="dob-box flex_lr bg-color-white"
		style="z-index:9999;top:{{44+statusBarHeight}}px">
		<view class="flex_m" hover-class="none" hover-stop-propagation="false">
      <image class="ic_lable_patient ml20" src="{{counselorInfo.avatar ? counselorInfo.avatar : '/static/images/counselor_default_avatar.png'}}" />
			<view class="doc-content ml20">
				<view class="flex_m">
					<view class="f28 c333 b">
            {{counselorInfo.name}}
					</view>
					<view class="f22 c666 ml20">
            {{counselorInfo.positionName ? counselorInfo.positionName : ''}}
					</view>
				</view>
			</view>
		</view>
    <view class="flex_m" catchtap="handleRefresh">
      <image class="mr20" style="width: 112rpx;height:48rpx;" src="/static/images/ic_refresh.png" />
    </view>
	</view>
	<!-- 聊天模块 -->
	<import src='./template/consult.wxml' />
	<template is='consult'
		data="{{messageArr,baseUrl,previewList,doctorId,scrollIntoView,scrollHeight,scrollTop,patientRemarkInfo,static,userInfo,avatar}}"></template>
	<!--底部输入框  -->
  <view id="chatBtn"
  class='chatinput-wrapper bg-color-white flex_m {{inputBottom==0 ? "envBottom":""}}' style='bottom:{{inputBottom}}px'
  catchtap='stopEventPropagation'>
  <view class='chatinput-content'  wx:if="{{!outService}}">
    <input adjust-position="{{false}}" value='{{inputValue}}' focus='{{focusFlag}}'
      bindfocus='inputFocus' bindblur='inputBlur' bindinput="oninput" bindconfirm='inputSend'
      bindkeyboardheightchange='onkeyboardheightchange' placeholder-class="c999" class='chatinput-input ml25'
      placeholder="请输入您想回复的内容" confirm-type='send' maxlength="1000"></input>
    <image src='/static/images/chat-phone.png' catchtap='chooseImageToSend'
      class='chatinput-img ml10 mr25'></image>
  </view>
	  <view class='chatinput-content outService' wx:if="{{outService}}">
		  <text>该咨询师已关闭诊前咨询服务</text>
	  </view>
</view>
</view>
