// pages/user/address/index.js
var api = require('../../config/api')
var util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '选择门店',
    shopList: [],
    current: 0,
    type: null,
    static: {
      nomes: api.ImgUrl + 'images/nomes.png',
      ic_evaluate_selected: api.ImgUrl + 'images/ic_evaluate_selected.png',
      ic_evaluate_unselected: api.ImgUrl + 'images/ic_evaluate_unselected.png',
      ic_disable_unselected: api.ImgUrl + 'images/ic_disable_unselected.png'
    },
    active: null
  },
  // 选择收货地址
  handleChoose(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      active: index
    })

    var pages = getCurrentPages()
    var prevPage = pages[pages.length - 2]
    prevPage.setData({
      shopActive: index
    })
    wx.navigateBack({
      delta: 1
    })

  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      shopList: app.globalData.shopList,
      active: options.index
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  }
})
