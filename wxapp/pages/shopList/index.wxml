<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="shop-list">
        <view wx:for="{{shopList}}" wx:key="index" class="shop-item {{!item.isChoose ? 'disabled' : ''}}" bindtap="handleChoose" data-index="{{index}}">
            <view class="shop-item-title" >
                <view>
                    {{item.name}}
                </view>
                <image wx:if="{{!item.isChoose}}" slot="icon" class="icon" src="/static/images/ic_disable_selected.png" />
                <image wx:else slot="icon" class="icon" src="{{ active == index ? static.ic_evaluate_selected :static.ic_evaluate_unselected }}" />
            </view>
            <view class="shop-item-subTitle">
                <text>{{item.headMan}}</text> <text class="ml20">{{item.headManPhone}}</text>
            </view>
            <view class="shop-item-address">
                {{item.address}}
            </view>
            <view class="shop-item-tips" wx:if="{{item.lackRemark}}">
               {{item.lackRemark}}
            </view>
        </view>
</view>
