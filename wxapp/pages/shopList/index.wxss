page{
    background: #F8F8F8;
}
.shop-list{
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx;
}
.shop-item{
    background: #FFFFFF;
    border-radius: 20rpx;
    box-sizing: border-box;
    padding: 20rpx;
    margin-bottom: 20rpx;
}

.shop-item-title{
    height: 44rpx;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
}
.shop-item-title image{
    width: 44rpx;
    height: 44rpx;
    display: block;
}
.shop-item-subTitle{
    padding-top: 20rpx;
    padding-bottom: 8rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    line-height: 40rpx;
}
.ml36{
    margin-left: 36rpx;
}
.shop-item-address{
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
    font-size: 28rpx;
}
.shop-item-tips{
    padding-top: 20rpx;
    color: #F05542;
    font-size: 24rpx;
}
.disabled{
    opacity: 1;
}
.disabled .shop-item-title ,.disabled .shop-item-subTitle,.disabled  .shop-item-address{
    color: #999 !important;
}
