<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="container" wx:if='{{detail.title}}' bindtouchmove="touchMove">

	<view class="head rel bg-color-white mt10">
		<view class="tc f36 b pt30">{{detail.title}}</view>
		<image src="{{imgObject.ic_prescription_seal}}"></image>
	</view>
	<view class="info pt20 f24 pb30">
		<view class="flex_lr_m">
			<view class="flex1">编号：{{detail.serialNumber}}</view>
			<view class="flex1">时间：{{detail.recomTime}}</view>
		</view>
		<view class="flex_lr_m pt10">
			<view class="flex1">姓名：{{detail.patientName}}</view>
			<view class="flex1">性别：{{detail.patientGender ? '男' : '女'}}</view>
		</view>
		<view class="flex_lr_m pt10">
			<view class="flex1">年龄：{{detail.patientAge}}{{detail.patientAgeUnit}}</view>
			<view class="flex1">科室：{{detail.departmentName}}</view>
		</view>
		<view class="pt10">
			辨证：{{detail.syndrome}}
		</view>
		<view class="pt10">
			辨病：{{detail.disease}}
			<!-- <text wx:for="{{detail.diagnosisList}}" wx:for-item="diagnosis">
				<block wx:if="{{index!=0}}">、</block>{{diagnosis}}
			</text> -->
		</view>
	</view>
	<view class="rel pt30 pb30 info mt10">
		<view>
			<text class="f32 c333">·处方·</text> <text
				class="rel-tag f26 ml20">{{detail.dosageForm == 1 ? '滋补膏方':'内服中药'}}</text>
		</view>
		<view class="f28 c333 b mt20">
			Rp：共{{detail.items.length}}味药
		</view>
		<view class="drug_list mt20 pb20 bb1">
			<view class="drug_list_item c333 f28" wx:for="{{detail.items}}">
				{{item.name}}
			</view>
		</view>
		<!-- <view class="drug_list mt20 pb20 bb1" wx:if="{{detail.status==1 || detail.status==2 || detail.status==4}}">
			<view class="drug_list_item c333 f28" wx:for="{{detail.items}}">
				{{item.name}}
			</view>
		</view>
		<view class="drug_encrypt flex_c_m f24 c666" wx:else>
			共{{detail.items.length}}味药，支付后可查看处方
		</view> -->
		<view class="pt30">
			<view class="c666 f28" wx:if="{{detail.dosageForm == 1}}">【剂量】共<text class="color-danger"> {{detail.tcmQuantity || 0}}</text> 剂，每日 <text
					class="color-danger">{{detail.tcmDosage || 0}}</text> 次，每次<text
					class="color-danger"> {{detail.tcmDosageCycle || 0}} </text>{{detail.tcmDosageCycleUnit==1?'克':'毫升'}}</view>
			<view class="c666 f28" wx:else>
				【剂量】共<text class="color-danger"> {{detail.tcmQuantity || 0}} </text>剂，每日 <text class="color-danger"> {{detail.tcmDosage || 0}} </text>剂，每剂分<text class="color-danger"> {{detail.tcmDosageCycle || 0}} </text>次煎服
			</view>
			<view class="c666 f28 pt10" wx:if="{{detail.dosageForm==1}}">【用法】{{detail.usageWay==1?'内服':detail.usageWay==2?'外用':'熬膏内服' }}</view>
			<view class="c666 f28 pt10" wx:if="{{detail.dosageForm==1}}">【辅料】<text wx:for="{{detail.accessoriesList}}"
					wx:key="index" wx:for-item="item">{{item.name}} {{item.dose}}</text></view>
			<view class="c666 f28 pt10">【医嘱】{{ detail.remark?detail.remark:''}}</view>
		</view>
		<view class="tag f32 b" wx:if="{{detail.status==1 || detail.status==2 || detail.status==4  }}">已购买</view>
		<view class="tag f32 b" wx:elif="{{detail.expire==1  }}">已失效</view>
	</view>
	<view class="bg-color-white p30 mt10">
		<view class="f32 c333">
			·明细·
		</view>
		<view class="f28 c333 flex_lr_m mt30">
			<view>
				药费
			</view>
			<view>
			{{detail.doseSalePrice}}元*{{detail.tcmQuantity}}剂={{detail.totalDoseSalePrice}}元
			</view>
		</view>
		<view class="f28 c333 flex_lr_m mt10" wx:if="{{detail.dosageForm == 1}}">
			<view>
				制作费
			</view>
			<view>
				{{detail.productionFee}}元
			</view>
		</view>
		<view class="f28 c333 flex_lr_m mt10">
			<view>
				总价<text class="c999">(不含代煎费、快递费)</text>
			</view>
			<view class="color-danger">
				{{detail.totalPrice}}元
			</view>
		</view>
	</view>
	<view class="info pl30 pr30 mt10 bb1">
		<view class="pt30 pb30">
			<view class="lh40 c38BF87 f28">
				<!-- 0无需审核 1待审核 2已审核 -->
				<view wx:if="{{detail.checkingStatus == 1}}">
					<image src="{{imgObject.reviewedNot}}" class="icon"></image><text>待审核</text>
				</view>
				<view wx:elif="{{detail.checkingStatus == 0 || detail.checkingStatus == 2}}">
					<image src="{{imgObject.reviewedOk}}" class="icon"></image><text>已审核</text>
				</view>
				<view wx:else>
					<image src="{{imgObject.reviewedNo}}" class="icon"></image><text>审核不通过</text>
				</view>
			</view>
			<view class="pt20 lh34 c666 f28">
				医生：{{detail.doctorName}}
				<block wx:if="{{detail.handWriting && detail.drSealImage}}">
					<image src="{{detail.drSealImage}}" class="sign" mode="heightFix"></image>{{detail.doctorAuditTime}}
				</block>
			</view>
			<view wx:if="{{detail.pharmacistName}}" class="pt10 lh34 c666 f28">
				审核药师：{{detail.pharmacistName}}
				<block wx:if="{{detail.handWriting && detail.phSealImage}}">
					<image src="{{detail.phSealImage}}" class="sign" mode="heightFix"></image>{{detail.pharmacistAuditTime}}
				</block>
			</view>
			<view class="pt10 lh34 c666 f28" wx:if='{{detail.alPharmacistName}}'>
				调配/发药：{{detail.alPharmacistName}}
				<block wx:if="{{detail.alPhSealImage && detail.alPharmacistAuditTime}}">
					<image src="{{detail.alPhSealImage}}" class="sign" mode="heightFix"></image>{{detail.alPharmacistAuditTime}}
				</block>
			</view>
		</view>
		<view>
			<view wx:if="{{detail.handWriting == 0 && detail.showDoctorSignature}}" class="pt10 lh34 c666 f28">
				医生：{{detail.doctorName}}<text>{{detail.signatureContent}}</text>{{detail.doctorAuditTime}}
			</view>
			<view wx:if="{{detail.handWriting == 0 && detail.showPharmacistSignature}}" class="pt10 lh34 c666 f28">
				药师：{{detail.pharmacistName}}<text>{{detail.signatureContent}}</text>{{detail.pharmacistAuditTime}}
			</view>
		</view>
	</view>
	<view class="p30 f22 c999 bg-color-white">
    注：处方48小时内有效且只能购买一次，如需继续用药，需要重新提交申请，由医生确认后方可购买
  </view>
	<!-- <view class="p30 f22 c999 bg-color-white" wx:if="{{detail.expire==1 && detail.buy == 0 && detail.require == 0}}">
		处方已超过购买有效期，为了您的用药安全，需要重新提交申请，由医生确认后方可购买
	</view> -->
	<!-- 处方已经购买提示 -->
	<!-- <view class="p30 f22 c999 bg-color-white" wx:if="{{detail.buy == 1 && detail.require == 0}}">
    注:处方48小时内有效且只能购买一次失效后购买需要重新申请请提醒患者及时购买
	</view> -->
</view>
<!-- 原公众号逻辑判断 -->
<view class="fixed b0 l0 w100 bg-color-white">
	<view class="confir pl30 pr30">
		<button bind:tap="download">查看处方pdf</button>
		<button class="{{type == 1 || !(detail.buy == 1||detail.expire == 1||detail.invalid == 1) ? '' : 'buyAgain'}} flex1 ml20"
			data-id="{{detail.recommendId}}" bindtap="buyDrug">
			<block wx:if="{{!(type == 1 || !(detail.buy == 1||detail.expire == 1||detail.invalid == 1))}}">申请再次购买</block>
			<block wx:else> {{type == 1 ? '立即复诊' : '购买药品'}}</block>
		</button>
	</view>
</view>
<view class="fixed b0 l0 w100 bg-color-white">
	<view
		wx:if="{{(detail.expire==1 && detail.buy == 0 && detail.require == 0) || (detail.buy == 1 && detail.require == 0)}}"
		class="confir pl30 pr30">
		<button data-id="{{detail.recommendId}}" bindtap="applyFor">申请再次购买</button>
	</view>
</view>
<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>