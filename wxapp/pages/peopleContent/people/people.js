// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    type: null, //1. 图文问诊  2.视频问诊,
    source: null, //来源：1.医生详情 2.个人中心
    list: [],
    imgObject: {
      ic_address_edit: api.ImgUrl + 'images/ic_address_edit.png',
      ic_address_deleted: api.ImgUrl + 'images/ic_address_deleted.png',
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '选择就诊人',
    selfInfo: {
      name: '',
      idCard: '',
      phone: ''
    }
  },
  //添加人员方法
  addPeople: function(e) {
    const idcard = e.currentTarget.dataset.idcard
    const phone = e.currentTarget.dataset.phone
    const name = e.currentTarget.dataset.name
    if (this.data.list.length >= 8) {
      util.showToast({ title: '最多添加8人' })
      return false
    } else {
      wx.navigateTo({
        url: '/pages/peopleContent/addPeople/addPeople?type=' + this.data.type + '&doctorId=' + this.data.doctorId + '&source=' + this.data.source + '&name=' + name + '&phone=' + phone + '&idcard=' + idcard
      })
    }

  },
  // 人员详情
  peopleDetail: function(e) {
    const model = e.currentTarget.dataset.model//成人还是儿童
    const index = e.currentTarget.dataset.index
    const id = this.data.list[index].inquirerId
    wx.navigateTo({
      url: '/pages/peopleContent/detail/detail?model=' + model + '&inquirerId=' + id
    })
  },
  // 删除
  del(e) {
    var index = e.currentTarget.dataset.index
    var id = this.data.list[index].inquirerId
    var that = this
    util.showModal({
      content: '就诊人删除后不可恢复，历史就诊记录还会保留，您确认删除吗？',
      showCancel: true,
      cancelText: '取消',
      cancelColor: '#666666',
      confirmText: '确认',
      success: function(res) {
        if (!res.cancel) {
          util.request(api.delPeople, { inquirerId: id }, 'post', 2)
            .then(res => {
              if (res.data.code === 0) {
                util.showToast({
                  title: '删除成功',
                  icon: 'success',
                  duration: 1000
                })
                setTimeout(() => {
                  that.getList()
                }, 1000)

              } else {
                util.showToast({ 'title': res.data.msg })
              }
            })
            .catch(res => {
              console.log(res)
            })
        }
      }
    })
  },
  // 选择人员
  choosePeople(e) {
    // const type = this.data.type
    // const doctorId = this.data.doctorId
    const inquirerId = e.currentTarget.dataset.id
    const index = e.currentTarget.dataset.index
    if (this.data.source === 1) {
      if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
        util.showToast({ title: '当前就诊人信息未实名，请先进行实名认证！' })
        return false
      }
      var pages = getCurrentPages()
      var prevPage = pages[pages.length - 2]
      prevPage.setData({
        ['info.inquirerId']: inquirerId,
        isReset: true
      })
      wx.navigateBack({
        delta: 1
      })
    } else {
      return false
    }
  },

  // 就诊人列表
  async getList() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    var data = await util.getPeopleList()
    util.hideLoading()
    data.forEach((element, index) => {
      if (element.relation === 0) {
        this.setData({
          ['selfInfo.name']: element.name ? element.name : '',
          ['selfInfo.idCard']: element.idCard ? element.idCard : '',
          ['selfInfo.phone']: element.phone ? element.phone : ''
        })
      }
      element.guardianIdCard = util.stringHidden(element.guardianIdCard, 6, 4)
      element.guardianPhone = util.stringHidden(element.guardianPhone, 3, 4)
      element.idCard = util.stringHidden(element.idCard, 6, 4)
      element.phone = util.stringHidden(element.phone, 3, 4)
    })

    this.setData({
      list: data
      // list: [{idCard:null,idCard:null,childTag:0,relationName:'本人',name: "五"}]
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      type: options.type * 1,
      source: options.source * 1,
      doctorId: options.doctorId,
      navTitle: options.source * 1 === 1 ? '选择就诊人' : '就诊人管理'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
