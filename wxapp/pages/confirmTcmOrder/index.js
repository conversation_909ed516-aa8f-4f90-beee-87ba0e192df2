// pages/orderTcm/orderTcm.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '确认订单',
    titleColor: '#fff',
    imgObject: {
      ic_address: api.ImgUrl + 'images/ic_address.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png',
      img_order_bar: api.ImgUrl + 'images/img_order_bar.png',
      ic_no_address: api.ImgUrl + 'images/ic_no_address.png'
    },
    shippingInfoId: null,
    address: null,
    addList: [],
    info: {},
    drugFunc: '',
    processingFlag: 1,
    orderDetail: {
    },
    remark: '',
    transAction: [{ name: '物流配送', id: 1 }, { name: '门店自提', id: 2 }],
    actions: [{ name: '不需要代煎' }, { name: '需要代煎' }],
    showPicker: false,
    showModel: null,
    transFlag: 0,
    templateId: [],
    deliveryMode: 1,
    shopList: [],
    shopActive: null
  },
  handleDeliveryMode(e) {
    this.setData({
      deliveryMode: e.currentTarget.dataset.type
    })
    if (e.currentTarget.dataset.type == 2) {
      if (this.data.shopList.length > 1 && this.data.shopActive == null) {
        wx.navigateTo({
          url: '/pages/shopList/index?index=' + this.data.shopActive
        })
      }
    }
  },
  // 获取模板
  getTemplate() {
    util.request(api.templates + '?type=3')
      .then(res => {
        if (res.data.code === 0) {
          const arry = []
          for (var i = 0; i < res.data.data.length; i++) {
            arry.push(res.data.data[i].templateId)
          }
          this.setData({
            templateId: arry
          })
        }
      })
      .catch(res => {
      })
  },
  showModelFun(e) {
    if (e.currentTarget.dataset.type * 1 === 2 && this.data.orderDetail.shippingMethod.length <= 1) {
      return false
    }
    this.setData({
      showPicker: true,
      showModel: e.currentTarget.dataset.type * 1
    })
  },
  onCancel() {
    this.setData({
      showPicker: false,
      showModel: null
    })
  },
  confirActions(e) {
    this.setData({
      processingFlag: e.detail.index,
      showPicker: false,
      showModel: null
    })
    this.totalCountFun()
  },
  confirTrans(e) {
    this.setData({
      transFlag: e.detail.index,
      showPicker: false,
      showModel: null
    })
  },
  // 获取地址列表
  async getAddressList() {
    var that = this
    try {
      var {
        data
      } = await util.request(api.addresesList, {
        userAddress: 1
      }, 'GET')
      if (data.code !== 0) {
        that.setData({
          ['info.shippingInfo']: null,
          shippingInfoId: null
        })
        util.showToast({
          title: data.msg,
          icon: 'none'
        })
        return false
      }
      var id = that.data.shippingInfoId
      var list = data.data
      const flag = list.some((item) => item.id === id || item.defaultAddr === 1)
      if (!flag) {
        that.setData({
          ['info.shippingInfo']: null,
          shippingInfoId: null
        })
      } else {
        const chooseData = list.find((item) => item.id === id)
        const defaultData = list.find((item) => item.defaultAddr === 1)
        const newData = chooseData ? chooseData : defaultData
        const data = {
          shippingInfoId: newData.id,
          fullAddress: newData.province + newData.city + newData.county + newData.addr,
          receiver: newData.receiver,
          phone: newData.phone
        }
        that.setData({
          ['info.shippingInfo']: data,
          shippingInfoId: newData.id
        })
      }
    } catch (error) {
      util.showToast({
        title: error.msg,
        icon: 'none'
      })
    }

  },
  // 取消订单提示
  handleCancel() {
    util.showModal({
      title: '',
      content: `取消订单后，再次购买需要咨询医生申请再次开方，确定取消吗？`,
      showCancel: true,
      cancelText: '我点错了',
      cancelColor: '#666666',
      confirmText: '确定',
      success: (result) => {
        if (result.confirm) {
          // this.cancelOrder()
        }
      }
    })
  },
  async initPage() {
    try {
      util.showLoading({})
      var parmas = {
        recomId: this.data.recomId,
        shippingInfoId: this.data.shippingInfoId
      }
      const { data } = await util.request(api.settlementInfo, parmas, 'get')
      if (data.code === 0) {
        const datac = data.data
        this.setData({
          orderDetail: datac,
          processingFlag: datac.processingFlag === 1 ? 1 : 0,
          shopList: data.data.boList
        })
        app.globalData.shopList = data.data.boList
        const { doseNum, price } = this.data.orderDetail
        //药费方程式
        const drugFunc = `${this.toFixedThree(price.doseUnitFee / 1000)}元*${doseNum}=${this.toFixedThree(price.totalDoseUnitFee / 1000) || 0}元`
        //代煎费方程式
        const diagnosisFunc = `${price.doseUnitProcessFee / 100}元 * ${doseNum} = ${price.totalProcessFee / 100 || 0}元`
        this.setData({
          drugFunc: drugFunc,
          diagnosisFunc: diagnosisFunc
        })
        this.totalCountFun()

        const id = data.data.warehouseBO ? data.data.warehouseBO.id : null
        if (id) {
          const index = data.data.boList.findIndex((item) => item.id == id)
          if (index !== -1) {
            this.setData({
              shopActive: data.data.boList[index].isChoose ? index : null
            })
          }

        } else {
          this.setData({
            shopActive: data.data.boList.length > 1 ? null : !data.data.boList[0].isChoose ? null : 0
          })
        }
      } else {
        util.showToast({ title: data.msg })
      }
      util.hideLoading({})
    } catch (err) {
      console.log(err)
      util.hideLoading({})
    }
  },
  // 计算商品总价
  totalCountFun() {
    // 药品总金额 , 总代煎费, 运费, 诊金 ,优惠券抵扣
    const { transFlag, processingFlag } = this.data
    const { totalAmount, totalProcessFee, freight, consultFee } = this.data.orderDetail.price
    const amount = (totalAmount) + (processingFlag ? (totalProcessFee) : 0) + (consultFee) + (transFlag ? (freight) : 0)
    this.setData({
      totalCount: (amount / 100).toFixed(2)
    })
  },
  toFixedThree(num) {
    return (Math.round(num * 1000) / 1000).toFixed(3)
  },
  // 提交订单
  async doPreOrder() {
    var that = this
    if (!that.data.orderDetail.shippingAddress) {
      util.showToast({ title: '请选择收获地址' })
      return
    }
    const transAction = this.data.transAction
    const orderDetail = this.data.orderDetail
    const params = {
      invoice: { isInvoice: 0 }, //不需要发票
      isProcessing: that.data.processingFlag, //代煎
      payType: 1, //微信支付
      recomId: that.data.recomId,
      remark: that.data.remark,
      shippingInfoId: orderDetail.shippingAddress.shippingInfoId, //收货地址ID
      shippingMethodId: this.data.deliveryMode,
      totalAmount: that.data.totalCount / 1 * 100 //商品总价
    }
    const { data } = await util.request(api.preOrder, params, 'post', 1)
    console.log(data)
    if (data.code === 0) {
      const orderId = data.data.orderId
      console.log(orderId)
      // debugger
      that.orderPay(orderId)
    } else {
      console.log('失败')
      util.showToast({ title: data.msg })
    }
  },
  paySuccess(orderNumber) {
    wx.redirectTo({
      url: '/pages/paySuccess/paySuccess?id=' + orderNumber + '&type=2'
    })
  },
  // 授权
  authSub(orderNumber) {
    var that = this
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        that.paySuccess(orderNumber)
      },
      fail(res) {
        that.paySuccess(orderNumber)
      }
    })
  },
  // 支付
  async orderPay(orderNumber) {
    try {
      const {
        data
      } = await util.request(util.getRealUrl(api.orderPay, orderNumber))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      wx.requestPayment({
        ...data.data,
        success: (result) => {
          this.authSub(orderNumber)
        },
        fail: () => {
          util.showModal({
            title: '支付结果',
            content: `您已取消支付，点击查看订单详情！`,
            showCancel: false,
            cancelText: '我点错了',
            cancelColor: '#666666',
            confirmText: '确定',
            success: (result) => {
              if (result.confirm) {
                wx.redirectTo({
                  url: '/pages/orderTcmDetail/index?orderId=' + orderNumber
                })
              }
            }
          })

        }
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  // 订单详情
  async getDetail() {
    try {
      const { data } = await util.request(util.getRealUrl(api.orderDetail, this.data.orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      console.log(data)
      return false
      this.setData({
        detail: data.data
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  onRadioChange(e) {
    this.setData({
      processingFlag: e.detail
    })
    this.totalCountFun()
    console.log(e,	'radio发生change事件，携带value值为：', e.detail.value)
  },
  changeNote(e) {
    this.setData({
      remark: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      recomId: options.id
    })
    this.getTemplate()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    await this.getAddressList()
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
