var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      backgroundImgUrl: api.ImgUrl + 'images/img_shopping_mall.png',
      search: api.ImgUrl + 'images/<EMAIL>', //搜索图标
      menu1: api.ImgUrl + 'images/bg_home_expert.png', //专家名医
      menu2: api.ImgUrl + 'images/img_video_consultation.png', //复诊开药
      bgImg: api.ImgUrl + 'images/bg_home.png', //底部背景图
      logo: api.ImgUrl + 'logo/ic_text.png', //logo
      img_blank_doctor: api.ImgUrl + 'images/img_blank_doctor.png',
      ic_font_message: api.ImgUrl + 'images/ic_font_message.png',
      img_blank_nomessage: api.ImgUrl + 'images/<EMAIL>',
      ic_suspension_car: api.ImgUrl + 'images/<EMAIL>',
      ic_my_more: api.ImgUrl + 'images/shop/ic_my_more.png'
    },
    swiperCurrent: 0,
    swiperList: [],
    featuredList: [],
    mallSections: [],
    hasMore: true,
    page: 1,
    imgUrls: [
      api.ImgUrl + 'images/img_home_banner.png',
      api.ImgUrl + 'images/img_home_banner.png'
    ],
    indicatorDots: false,
    autoplay: true,
    interval: 3000,
    duration: 500, //滑动动画时长
    circular: true,
    indicatorColor: 'rgba(255,255,255,1)', //普通轮播点背景色
    indicatorActiveColor: '#2e9cff', //选中轮播点背景色
    bannerList: [],
    bannerCurrent: 0,
    otcConsultBuying: true,
    zoneList: []
  },

  // 轮播图事件
  swiperChange(e) {
    const current = e.detail.current
    this.setData({
      swiperCurrent: current,
      bannerCurrent: current
    })
  },
  goSearch() {
    wx.navigateTo({
      url: '/pages/searchMerchandise/index'
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function(options) {
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    const applicationConfig = await util.getHaiNanConfig()
    wx.setStorageSync('applicationConfig', applicationConfig)
    this.setData({
      otcConsultBuying: applicationConfig.config.otcConsultBuying
    })
    this.getData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function() {
    const applicationConfig = await util.getHaiNanConfig()
    wx.setStorageSync('applicationConfig', applicationConfig)
    this.setData({
      otcConsultBuying: applicationConfig.config.otcConsultBuying
    })
    const classify = this.selectComponent('#classify')
    classify.getData()
    await this.getData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (!this.data.hasMore) {
      return
    }
    this.getProductList()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {},
  async getData() {
    this.setData({
      page: 1,
      hasMore: true
    }, async() => {
      await this.getSections()
      await this.getZoneList()
      await this.getProductList()
      await this.getBannerList()
    })
  },

  // 获取专区列表
  async getZoneList() {
    try {
      const response = await util.request(api.zoneList, { page: 1,num: 100, orderBy: 'sort' })
      const { code, data, msg} = response.data
      if (code === 0) {
        this.setData({
          zoneList: data.result
        })
      } else {
        util.showToast({
          title: msg,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      throw new Error(error)
    }
  },

  handleDetail(e) {
    const { type, id, url, content, video } = e.currentTarget.dataset
    if (type === 1) {
      if (!content && !video) return
      wx.navigateTo({
        url: `/pages/article/articleDetail/index?id=${id}&type=banner`
      })
      console.log('图文')
    } else {
      wx.navigateTo({
        url: `/pages/webView/index?url=${url}`
      })
      console.log('外部链接')
    }
  },
  async getProductList() {
    if (this.loading) return
    this.loading = true
    await util.request(api.productList, {
      featured: 1,
      page: this.data.page,
      num: 10
    }, 'get').then(res => {
      this.loading = false
      this.setData({
        featuredList: this.data.page === 1 ? res.data.data.result : this.data.featuredList.concat(res.data.data.result),
        hasMore: this.data.page < res.data.data.totalPages,
        page: this.data.page + 1
      })
    })
  },
  async getSections() {
    await util.request(api.mallSections).then(res => {
      this.setData({
        mallSections: res.data.data
      })
    })
  },
  handleaddCart() {
    this.shopCartNumber()
  },
  shopCartNumber() {
    util.request(api.cartsInfo).then(res => {
      const list = res.data.data.groups[0].items
      let number = 0
      list.forEach(item => {
        number += item.quantity
      })
      const fixedCart = this.selectComponent('#fixedCart')
      fixedCart.setNumber(number)
    })
  },
  async getBannerList() {
    try {
      const { data } = await util.request(api.bannerList, {sectionType: 2}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        bannerList: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  // 跳转专区商品详情
  toZoneDetail(e) {
    const { id, title } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/zoneDetail/zoneDetail?id=${id}&title=${title}`
    })
  }
})
