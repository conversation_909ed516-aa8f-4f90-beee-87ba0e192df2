# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz?cache=0&sync_timestamp=1612314861216&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13":
  version "7.12.13"
  resolved "https://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.12.13.tgz?cache=0&sync_timestamp=1612314861216&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.12.13.tgz#dcfc826beef65e75c50e21d3837d7d95798dd658"
  integrity sha1-3PyCa+72XnXFDiHTg319lXmN1lg=
  dependencies:
    "@babel/highlight" "^7.12.13"

"@babel/generator@^7.14.2":
  version "7.14.3"
  resolved "https://registry.nlark.com/@babel/generator/download/@babel/generator-7.14.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.14.3.tgz#0c2652d91f7bddab7cccc6ba8157e4f40dcedb91"
  integrity sha1-DCZS2R973at8zMa6gVfk9A3O25E=
  dependencies:
    "@babel/types" "^7.14.2"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-function-name@^7.14.2":
  version "7.14.2"
  resolved "https://registry.nlark.com/@babel/helper-function-name/download/@babel/helper-function-name-7.14.2.tgz?cache=0&sync_timestamp=1620839504196&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.14.2.tgz#397688b590760b6ef7725b5f0860c82427ebaac2"
  integrity sha1-OXaItZB2C273cltfCGDIJCfrqsI=
  dependencies:
    "@babel/helper-get-function-arity" "^7.12.13"
    "@babel/template" "^7.12.13"
    "@babel/types" "^7.14.2"

"@babel/helper-get-function-arity@^7.12.13":
  version "7.12.13"
  resolved "https://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.13.tgz?cache=0&sync_timestamp=1612314863557&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-get-function-arity%2Fdownload%2F%40babel%2Fhelper-get-function-arity-7.12.13.tgz#bc63451d403a3b3082b97e1d8b3fe5bd4091e583"
  integrity sha1-vGNFHUA6OzCCuX4diz/lvUCR5YM=
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-split-export-declaration@^7.12.13":
  version "7.12.13"
  resolved "https://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.13.tgz?cache=0&sync_timestamp=1612314863389&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-split-export-declaration%2Fdownload%2F%40babel%2Fhelper-split-export-declaration-7.12.13.tgz#e9430be00baf3e88b0e13e6f9d4eaf2136372b05"
  integrity sha1-6UML4AuvPoiw4T5vnU6vITY3KwU=
  dependencies:
    "@babel/types" "^7.12.13"

"@babel/helper-validator-identifier@^7.14.0":
  version "7.14.0"
  resolved "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.14.0.tgz?cache=0&sync_timestamp=1619727412592&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.14.0.tgz#d26cad8a47c65286b15df1547319a5d0bcf27288"
  integrity sha1-0mytikfGUoaxXfFUcxml0Lzycog=

"@babel/highlight@^7.10.4", "@babel/highlight@^7.12.13":
  version "7.14.0"
  resolved "https://registry.nlark.com/@babel/highlight/download/@babel/highlight-7.14.0.tgz?cache=0&sync_timestamp=1619727182056&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhighlight%2Fdownload%2F%40babel%2Fhighlight-7.14.0.tgz#3197e375711ef6bf834e67d0daec88e4f46113cf"
  integrity sha1-MZfjdXEe9r+DTmfQ2uyI5PRhE88=
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.0"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.12.13", "@babel/parser@^7.14.2", "@babel/parser@^7.7.0":
  version "7.14.3"
  resolved "https://registry.nlark.com/@babel/parser/download/@babel/parser-7.14.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fparser%2Fdownload%2F%40babel%2Fparser-7.14.3.tgz#9b530eecb071fd0c93519df25c5ff9f14759f298"
  integrity sha1-m1MO7LBx/QyTUZ3yXF/58UdZ8pg=

"@babel/template@^7.12.13":
  version "7.12.13"
  resolved "https://registry.nlark.com/@babel/template/download/@babel/template-7.12.13.tgz#530265be8a2589dbb37523844c5bcb55947fb327"
  integrity sha1-UwJlvooliduzdSOETFvLVZR/syc=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@babel/parser" "^7.12.13"
    "@babel/types" "^7.12.13"

"@babel/traverse@^7.7.0":
  version "7.14.2"
  resolved "https://registry.nlark.com/@babel/traverse/download/@babel/traverse-7.14.2.tgz?cache=0&sync_timestamp=1620839505000&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.14.2.tgz#9201a8d912723a831c2679c7ebbf2fe1416d765b"
  integrity sha1-kgGo2RJyOoMcJnnH678v4UFtdls=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@babel/generator" "^7.14.2"
    "@babel/helper-function-name" "^7.14.2"
    "@babel/helper-split-export-declaration" "^7.12.13"
    "@babel/parser" "^7.14.2"
    "@babel/types" "^7.14.2"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.12.13", "@babel/types@^7.14.2", "@babel/types@^7.7.0":
  version "7.14.2"
  resolved "https://registry.nlark.com/@babel/types/download/@babel/types-7.14.2.tgz?cache=0&sync_timestamp=1620839504820&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.14.2.tgz#4208ae003107ef8a057ea8333e56eb64d2f6a2c3"
  integrity sha1-QgiuADEH74oFfqgzPlbrZNL2osM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.0"
    to-fast-properties "^2.0.0"

"@eslint/eslintrc@^0.4.1":
  version "0.4.1"
  resolved "https://registry.nlark.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.1.tgz#442763b88cecbe3ee0ec7ca6d6dd6168550cbf14"
  integrity sha1-RCdjuIzsvj7g7Hym1t1haFUMvxQ=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

acorn-jsx@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-5.3.1.tgz?cache=0&sync_timestamp=1599499102891&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-jsx%2Fdownload%2Facorn-jsx-5.3.1.tgz#fc8661e11b7ac1539c47dbfea2e72b3af34d267b"
  integrity sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=

acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.nlark.com/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1620134156200&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn%2Fdownload%2Facorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.nlark.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1621517685940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.5.0"
  resolved "https://registry.nlark.com/ajv/download/ajv-8.5.0.tgz?cache=0&sync_timestamp=1621517685940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-8.5.0.tgz#695528274bcb5afc865446aa275484049a18ae4b"
  integrity sha1-aVUoJ0vLWvyGVEaqJ1SEBJoYrks=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-4.1.1.tgz#cbb9ae256bf750af1eab344f229aa27fe94ba348"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995625950&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz?cache=0&sync_timestamp=1618995625950&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.nlark.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npm.taobao.org/babel-eslint/download/babel-eslint-10.1.0.tgz?cache=0&sync_timestamp=1611946177416&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-eslint%2Fdownload%2Fbabel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1618995297666&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.1"
  resolved "https://registry.nlark.com/chalk/download/chalk-4.1.1.tgz?cache=0&sync_timestamp=1618995297666&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.1.tgz#c80b3fab28bf6371e6863325eee67e618b77e6ad"
  integrity sha1-yAs/qyi/Y3HmhjMl7uZ+YYt35q0=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

cross-spawn@^7.0.2:
  version "7.0.3"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.3.1"
  resolved "https://registry.npm.taobao.org/debug/download/debug-4.3.1.tgz#f0d229c505e0c6d8c49ac553d1b13dc183f6b2ee"
  integrity sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=
  dependencies:
    ms "2.1.2"

deep-is@^0.1.3:
  version "0.1.3"
  resolved "https://registry.nlark.com/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@^1.0.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-1.3.2.tgz?cache=0&sync_timestamp=1621256858583&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-1.3.2.tgz#6206437d32ceefaec7161803230c7a20bc1b4d91"
  integrity sha1-YgZDfTLO767HFhgDIwx6ILwbTZE=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.2.0.tgz?cache=0&sync_timestamp=1617298545989&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-2.2.0.tgz#9a0b6c2782ed6a1c7323d42267183df9bd8b1d57"
  integrity sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc=

domhandler@^4.0.0, domhandler@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/domhandler/download/domhandler-4.2.0.tgz?cache=0&sync_timestamp=1618563983578&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomhandler%2Fdownload%2Fdomhandler-4.2.0.tgz#f9768a5f034be60a89a27c2e4d0f74eba0d8b059"
  integrity sha1-+XaKXwNL5gqJonwuTQ9066DYsFk=
  dependencies:
    domelementtype "^2.2.0"

domutils@^2.5.2:
  version "2.6.0"
  resolved "https://registry.npm.taobao.org/domutils/download/domutils-2.6.0.tgz?cache=0&sync_timestamp=1618565562449&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-2.6.0.tgz#2e15c04185d43fb16ae7057cb76433c6edb938b7"
  integrity sha1-LhXAQYXUP7Fq5wV8t2Qzxu25OLc=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1614682920696&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

enquirer@^2.3.5:
  version "2.3.6"
  resolved "https://registry.nlark.com/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-plugin-html@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npm.taobao.org/eslint-plugin-html/download/eslint-plugin-html-6.1.2.tgz#fa26e4804428956c80e963b6499c192061c2daf3"
  integrity sha1-+ibkgEQolWyA6WO2SZwZIGHC2vM=
  dependencies:
    htmlparser2 "^6.0.1"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint@^7.26.0:
  version "7.27.0"
  resolved "https://registry.nlark.com/eslint/download/eslint-7.27.0.tgz#665a1506d8f95655c9274d84bd78f7166b07e9c7"
  integrity sha1-ZloVBtj5VlXJJ02EvXj3FmsH6cc=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.1"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "https://registry.npm.taobao.org/espree/download/espree-7.3.1.tgz?cache=0&sync_timestamp=1607144072939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fespree%2Fdownload%2Fespree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/esquery/download/esquery-1.4.0.tgz#2148ffc38b82e8c7057dfed48425b3e61f0f24a5"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1596641698640&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz?cache=0&sync_timestamp=1596641698640&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-5.2.0.tgz#307df42547e6cc7324d3cf03c155d5cdb8c53880"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz?cache=0&sync_timestamp=1591599659970&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-deep-equal%2Fdownload%2Ffast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz?cache=0&sync_timestamp=1576340291001&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz?cache=0&sync_timestamp=1595428045324&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-levenshtein%2Fdownload%2Ffast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.nlark.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/flatted/download/flatted-3.1.1.tgz?cache=0&sync_timestamp=1611059459175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fflatted%2Fdownload%2Fflatted-3.1.1.tgz#c4b489e80096d9df1dfc97c79871aea7c617c469"
  integrity sha1-xLSJ6ACW2d8d/JfHmHGup8YXxGk=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

glob-parent@^5.0.0:
  version "5.1.2"
  resolved "https://registry.nlark.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1620073671816&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.3:
  version "7.1.7"
  resolved "https://registry.nlark.com/glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1620337382269&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz#3b193e9233f01d42d0b3f78294bbeeb418f94a90"
  integrity sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.nlark.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://registry.nlark.com/globals/download/globals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globals@^13.6.0:
  version "13.8.0"
  resolved "https://registry.nlark.com/globals/download/globals-13.8.0.tgz#3e20f504810ce87a8d72e55aecf8435b50f4c1b3"
  integrity sha1-PiD1BIEM6HqNcuVa7PhDW1D0wbM=
  dependencies:
    type-fest "^0.20.2"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

htmlparser2@^6.0.1:
  version "6.1.0"
  resolved "https://registry.nlark.com/htmlparser2/download/htmlparser2-6.1.0.tgz#c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-core-module@^2.2.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/is-core-module/download/is-core-module-2.4.0.tgz#8e9fc8e15027b011418026e98f0e6f4d86305cc1"
  integrity sha1-jp/I4VAnsBFBgCbpjw5vTYYwXME=
  dependencies:
    has "^1.0.3"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz?cache=0&sync_timestamp=1619345098261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-tokens%2Fdownload%2Fjs-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1618435028703&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.taobao.org/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npm.taobao.org/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/lru-cache/download/lru-cache-6.0.0.tgz?cache=0&sync_timestamp=1594427567713&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flru-cache%2Fdownload%2Flru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1607433926553&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npm.taobao.org/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1617971675964&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.nlark.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

regexpp@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/regexpp/download/regexpp-3.1.0.tgz#206d0ad0a5648cffbdb8ae46438f3dc51c9f78e2"
  integrity sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve@^1.12.0:
  version "1.20.0"
  resolved "https://registry.npm.taobao.org/resolve/download/resolve-1.20.0.tgz#629a013fb3f70755d6f0b7935cc1c2c5378b1975"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

semver@^7.2.1:
  version "7.3.5"
  resolved "https://registry.npm.taobao.org/semver/download/semver-7.3.5.tgz#0b621c879348d8998e4b0e4be94b3f12e6018ef7"
  integrity sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=
  dependencies:
    lru-cache "^6.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-4.0.0.tgz?cache=0&sync_timestamp=1618555008681&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fslice-ansi%2Fdownload%2Fslice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

source-map@^0.5.0:
  version "0.5.7"
  resolved "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

string-width@^4.2.0:
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-4.2.2.tgz#dafd4f9559a7585cfba529c6a0a4f73488ebd4c5"
  integrity sha1-2v1PlVmnWFz7pSnGoKT3NIjr1MU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&sync_timestamp=1618553299612&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1618560998281&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1618560998281&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

table@^6.0.9:
  version "6.7.1"
  resolved "https://registry.nlark.com/table/download/table-6.7.1.tgz#ee05592b7143831a8c94f3cee6aae4c1ccef33e2"
  integrity sha1-7gVZK3FDgxqMlPPO5qrkwczvM+I=
  dependencies:
    ajv "^8.0.1"
    lodash.clonedeep "^4.5.0"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.0"
    strip-ansi "^6.0.0"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1621402383646&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.8.1.tgz?cache=0&sync_timestamp=1621402383646&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237517218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=
