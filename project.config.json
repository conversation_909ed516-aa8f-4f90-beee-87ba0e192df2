{"appid": "touristappid", "compileType": "miniprogram", "libVersion": "2.25.3", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}